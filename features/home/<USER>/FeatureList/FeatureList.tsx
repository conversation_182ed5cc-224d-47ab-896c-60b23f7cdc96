import CameraV2Icon from '@/assets/icons/camera-v2-icon.svg'
import ExaminationIcon from '@/assets/icons/examination-document.svg'
import FacultyPositionIconV2 from '@/assets/icons/faculty-position-pin-v2.svg'
import HeartBeatIcon from '@/assets/icons/heartbeat-icon.svg'
import MedicalDocumentsV2Icon from '@/assets/icons/medical-document-v2-icon.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
import { APP_ROUTES } from '@/routes/appRoutes'
import * as Haptics from 'expo-haptics'
import { LinearGradient } from 'expo-linear-gradient'
import { Link, LinkProps } from 'expo-router'
import React from 'react'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
const FEATURE_LIST = {
  MEDICAL_HANDBOOK: {
    title: 'MES-753',
    icon: FacultyPositionIconV2,
    url: APP_ROUTES?.MEDICAL_HANDBOOK?.path,
  },
  MEDICAL_DOCUMENTS: {
    title: 'MES-793',
    icon: MedicalDocumentsV2Icon,
    url: APP_ROUTES?.MEDICAL_DOCUMENTS?.path,
  },
  CHAT_BOT_SEARCH_MEDICINE: {
    title: 'MES-754',
    icon: CameraV2Icon,
    url: APP_ROUTES?.CHAT_BOT?.children?.CHAT_BOT_SEARCH_MEDICINE?.path,
  },
  EXAMINATION: {
    title: 'MES-755',
    icon: ExaminationIcon,
    url: APP_ROUTES?.EXAMINATION?.path,
  },
}

export const FeatureList = () => {
  const { user } = useAuthentication()
  const { t } = useTranslation()
  return (
    <View className="flex-col gap-4 px-4">
      <View className="flex-row items-center gap-2">
        <Text size="heading8" variant="primary">
          {t('MES-752')}
        </Text>
        <HeartBeatIcon width={24} height={24} />
      </View>

      <View>
        <Link href={APP_ROUTES.MEDICAL_FACULTIES.path as LinkProps['href']}>Medical</Link>
      </View>

      <View className="flex flex-col gap-3">
        <View className="flex h-[130px] flex-row gap-3 ">
          {/* Facultty */}
          <Link
            href={
              user
                ? (FEATURE_LIST.MEDICAL_HANDBOOK.url as LinkProps['href'])
                : (APP_ROUTES?.LOGIN?.path as LinkProps['href'])
            }
            asChild
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
          >
            <TouchableOpacity className="min-w-1/2 h-full flex-1">
              <LinearGradient
                colors={['#4C9DFF', '#8DFFE4']}
                start={{ x: 0, y: 1 }}
                end={{ x: 1, y: 0 }}
                className="flex  justify-end rounded-lg p-4"
                style={{
                  borderRadius: 12,
                  flex: 1,
                  height: '100%',
                  padding: 12,
                }}
              >
                <View className="mt-auto flex flex-col justify-center gap-y-3">
                  <FEATURE_LIST.MEDICAL_HANDBOOK.icon width={50} height={50} />
                  <Text size="body10" variant="white">
                    {t(FEATURE_LIST.MEDICAL_HANDBOOK.title)}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </Link>
          {/*  Find medicine by photo */}
          <Link
            href={FEATURE_LIST.CHAT_BOT_SEARCH_MEDICINE.url as LinkProps['href']}
            asChild
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
          >
            <TouchableOpacity className="h-full flex-1">
              <LinearGradient
                colors={['#FF3A6E', '#FFCF82']}
                start={{ x: 0, y: 1 }}
                end={{ x: 1, y: 0 }}
                className="flex justify-end rounded-lg "
                style={{
                  borderRadius: 12,
                  height: '100%',
                  flex: 1,
                  padding: 12,
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <View className="flex flex-col justify-center gap-y-3">
                  <FEATURE_LIST.CHAT_BOT_SEARCH_MEDICINE.icon width={50} height={50} />
                  <Text size="body10" variant="white">
                    {t(FEATURE_LIST.CHAT_BOT_SEARCH_MEDICINE.title)}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </Link>
        </View>
        <View className="flex h-[130px] flex-row gap-3">
          {/*  Medical documents */}
          <Link
            href={FEATURE_LIST.MEDICAL_DOCUMENTS.url as LinkProps['href']}
            asChild
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
          >
            <TouchableOpacity className="h-full flex-1">
              <LinearGradient
                colors={['#E166F9', '#BB6FFE']}
                start={{ x: 1, y: 0 }}
                end={{ x: 0, y: 1 }}
                className="flex justify-end rounded-lg "
                style={{
                  borderRadius: 12,
                  height: '100%',
                  flex: 1,
                  padding: 12,
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <View className="flex flex-col justify-center gap-y-3">
                  <FEATURE_LIST.MEDICAL_DOCUMENTS.icon width={50} height={50} />
                  <Text size="body10" variant="white">
                    {t(FEATURE_LIST.MEDICAL_DOCUMENTS.title)}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </Link>
          {/*Medical dictionary */}
          <Link
            href={FEATURE_LIST.EXAMINATION.url as LinkProps['href']}
            asChild
            onPress={() => {
              Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light)
            }}
          >
            <TouchableOpacity className="h-full flex-1">
              <LinearGradient
                colors={['#8BC34A', '#CDDC39']}
                start={{ x: 0, y: 1 }}
                end={{ x: 1, y: 0 }}
                className="flex flex-1 justify-end rounded-lg p-3"
                style={{
                  borderRadius: 12,
                  flex: 1,
                  height: '100%',
                  padding: 12,
                  display: 'flex',
                  justifyContent: 'center',
                }}
              >
                <View className="flex flex-col justify-center gap-y-3">
                  <FEATURE_LIST.EXAMINATION.icon width={50} height={50} />
                  <Text size="body10" variant="white">
                    {t(FEATURE_LIST.EXAMINATION.title)}
                  </Text>
                </View>
              </LinearGradient>
            </TouchableOpacity>
          </Link>
        </View>
      </View>
    </View>
  )
}
