import { API_ENDPOINTS } from '@/constants/endpoint.constant'
import { httpService } from '@/services/http/http.service'
import { Faculty } from '@/types/faculty.type'
import { PaginatedDocs } from '@/types/global.type'

import { TextToSpeech } from '@/types/audio.type'
import { Params } from '@/types/http.type'
import { AxiosRequestConfig } from 'axios'
import { TextToSpeechPayload } from '../types'
import { sentenceByAIPayload } from '../hooks/useGenerateSentenceByAI'

// SERVER / CLIENT
class MedicalFacultyService {
  private static instance: MedicalFacultyService

  private constructor() { }

  public static getInstance(): MedicalFacultyService {
    if (!MedicalFacultyService.instance) {
      MedicalFacultyService.instance = new MedicalFacultyService()
    }
    return MedicalFacultyService.instance
  }

  async getMedicalFaculties({
    params = {},
    options = {},
  }: {
    params?: Params
    options?: AxiosRequestConfig
  } = {}): Promise<PaginatedDocs<Faculty>> {
    const data = await httpService.get<PaginatedDocs<Faculty>>(`/${API_ENDPOINTS.faculties_api}`, {
      params,
      ...options,
    })
    return data
  }

  async getTextToSpeech({
    payload,
    options = {},
  }: {
    payload: TextToSpeechPayload
    options?: AxiosRequestConfig,
  }): Promise<TextToSpeech> {
    const data = await httpService.post<TextToSpeech>(`/${API_ENDPOINTS.text_to_speech_api}`, payload, options)
    return data
  }

  async generateSentenceByAI({
    payload,
    options = {},
  }: {
    payload: sentenceByAIPayload
    options?: AxiosRequestConfig,
  }): Promise<any> {
    const data = await httpService.post<any>(`https://bot.hico.co.jp/v1/workflows/run`, payload, options)
    return data
  }
}

export const medicalFacultyService = MedicalFacultyService.getInstance()
