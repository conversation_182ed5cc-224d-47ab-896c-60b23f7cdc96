import { ActivityIndicator, View } from 'react-native'

import { useLocalSearchParams } from 'expo-router'
import { useEffect } from 'react'
import { HeaderBarDetail } from '../../components/common/HeaderBarDetail'
import { MedicalFacultiesDetailContent } from '../../components/detail/MedicalFacultiesDetailContent'
import { MedicalFacultiesRecord } from '../../components/quick-edit/MedicalFacultiesRecord'
import { useGetFacultyDetail } from '../../hooks/useGetFacultyDetail'
import { MedicalFacultiesContent } from '../../layouts/MedicalFacultiesContent'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const MedicalFacultyDetailWrapper = () => {
  const {
    isLoading,
    reset,
    hiddenLoading,
    setSentence,
    setKeywords,
    isFinishInType,
    saveFacultyDetail,
    getStatusEditMode,
    updateStatusEditMode,
  } = useMedicalFacultiesStore()

  const { id } = useLocalSearchParams()

  const { facultyDetail, isGetFacultyDetailLoading } = useGetFacultyDetail({
    id: id as string,
    params: {
      locale: 'all',
      select: {
        sampleSentences: true,
        name: true,
      },
    },
    useQueryOptions: {
      enabled: Boolean(id),
    },
  })

  useEffect(() => {
    if (facultyDetail) {
      saveFacultyDetail(facultyDetail)
    }
  }, [facultyDetail, saveFacultyDetail])

  useEffect(() => {
    let timer: number
    if (isLoading) {
      timer = setTimeout(() => {
        hiddenLoading()
        setSentence({
          vi: 'Chào bác sĩ, tôi bị [677d4fb58d7dec6926ec0fc9] [677d4ff08d7dec6926ec1023] từ [45XETNVB9K], tình trạng nghiêm trọng hơn khi [45XETNVB6K]. Đau nhiều ở [45XETNVB5K].',
          ja: 'こんにちは、[677d4fb58d7dec6926ec0fc9]から[677d4ff08d7dec6926ec1023]と[45XETNVB9K]いがあります。[45XETNVB6K]で症状が悪化します。[45XETNVB5K]が特に痛みます。',
        })

        setKeywords({
          '677d4fb58d7dec6926ec0fc9': {
            id: '677d4fb58d7dec6926ec0fc9',
            name: {
              vi: 'đau đầu',
              ja: '頭痛',
            },
            type: 'symptom',
            hiragana: 'はらいたい',
          } as any,
          '677d4ff08d7dec6926ec1023': {
            id: '677d4ff08d7dec6926ec1023',
            name: {
              vi: 'chóng mặt',
              ja: 'めまい',
            },
            type: 'symptom',
            hiragana: 'あたまいたい',
          } as any,
          '45XETNVB9K': {
            id: '45XETNVB9K',
            name: {
              vi: 'hôm qua',
              ja: '昨日',
            },
            type: 'when',
            hiragana: 'はらいたい',
          } as any,
          '45XETNVB6K': {
            id: '45XETNVB6K',
            name: {
              vi: 'đi lên xuống cầu thang',
              ja: '階段を上り下りする',
            },
            type: 'while',
            hiragana: 'はらいたい',
          } as any,
          '45XETNVB5K': {
            id: '45XETNVB5K',
            name: {
              vi: 'thái dương',
              ja: 'こめかみ',
            },
            type: 'position',
            hiragana: 'はらいたい',
          } as any,

          '45XETNVB4K': {
            id: '45XETNVB4K',
            name: {
              vi: 'Chào bác sĩ',
              ja: 'こんにちは',
            },
            type: 'position',
            hiragana: 'はらいたい',
          } as any,
        })

        updateStatusEditMode(true)
      }, 3000)
    }

    return () => {
      clearTimeout(timer)
    }
  }, [isLoading, hiddenLoading, setSentence, setKeywords, updateStatusEditMode])

  useEffect(() => {
    return () => {
      reset()
    }
  }, [])

  if (isGetFacultyDetailLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator size="large" />
      </View>
    )
  }

  return (
    <View className="z-10 flex-1 bg-white ">
      <HeaderBarDetail />

      <MedicalFacultiesContent>
        {isFinishInType() && getStatusEditMode() ? (
          <MedicalFacultiesRecord />
        ) : (
          <MedicalFacultiesDetailContent />
        )}
      </MedicalFacultiesContent>
    </View>
  )
}
