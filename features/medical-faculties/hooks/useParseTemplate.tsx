/**
 * Chia nhỏ template thành các phần text và placeholder
 * Ví dụ: '<PERSON><PERSON><PERSON> bác sĩ, tô<PERSON> bị [ID1] từ [ID2].'
 * -> response: [
 * { type: 'text', content: '<PERSON><PERSON><PERSON> bác sĩ, tôi bị ' },
 * { type: 'placeholder', id: 'ID1' },
 * { type: 'text', content: ' từ ' },
 * { type: 'placeholder', id: 'ID2' },
 * { type: 'text', content: '.' }
 * ]
 */

type ParsedPart = {
  type: 'text' | 'placeholder'
  content?: string
  id?: string
}
export const useParseTemplate = () => {
  const parseTemplate = (templateString: string) => {
    const regex = /(\[[^\]]+\])/g
    let parts: ParsedPart[] = []
    let lastIndex = 0

    templateString?.replace(regex, (match, p1, offset) => {
      if (offset > lastIndex) {
        parts.push({
          type: 'text',
          content: templateString.substring(lastIndex, offset),
        })
      }

      const idContent = p1.substring(1, p1.length - 1)
      parts.push({
        type: 'placeholder',
        id: idContent,
      })

      lastIndex = offset + match.length
      return match
    })

    if (lastIndex < templateString.length) {
      parts.push({
        type: 'text',
        content: templateString.substring(lastIndex),
      })
    }

    return parts
  }
  return {
    parseTemplate,
  }
}
