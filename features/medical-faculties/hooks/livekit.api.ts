import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import Constants from 'expo-constants'
import { z } from 'zod'

// Response Schema
const ConnectionDetailsSchema = z.object({
  server_url: z.string(),
  participant_token: z.string(),
  room_name: z.string().optional(),
  participant_name: z.string().optional(),
})

export type ConnectionDetailsResponse = z.infer<typeof ConnectionDetailsSchema>

// Request Arguments
export type CreateTokenArgs = {
  room_name?: string
  participant_name?: string
  participant_identity?: string
  participant_metadata?: Record<string, string>
  participant_attributes?: Record<string, string>
  agent_name?: string
}

/**
 * Get the FastAPI backend base URL for LiveKit token generation
 */
function getBackendApiUrl(): string {
  // In development, default to localhost FastAPI server
  if (__DEV__) {
    const fastAPILocalhostUrl = 'https://giovanny-preready-hyponastically.ngrok-free.dev'
    return fastAPILocalhostUrl
  }

  // Try environment variable first (for FastAPI backend)
  if (Constants.expoConfig?.extra?.livekitBackendUrl) {
    return Constants.expoConfig.extra.livekitBackendUrl as string
  }

  // In production, backend URL must be configured
  throw new Error(
    'LiveKit backend URL not configured. ' +
      'Please set livekitBackendUrl in app.json extra field or use EXPO_PUBLIC_LIVEKIT_BACKEND_URL environment variable.',
  )
}

/**
 * Create LiveKit token service function
 */
async function createLivekitToken(args: CreateTokenArgs): Promise<ConnectionDetailsResponse> {
  try {
    const backendUrl = getBackendApiUrl()
    const apiUrl = `${backendUrl}/create-token`

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(args),
    })

    if (!response.ok) {
      const errorText = await response.text()
      let errorData: { detail?: string; error?: string }

      try {
        errorData = JSON.parse(errorText)
      } catch {
        errorData = { error: errorText || `HTTP ${response.status}` }
      }

      throw new Error(errorData.detail || errorData.error || response.statusText)
    }

    const data = await response.json()
    const parsedData = ConnectionDetailsSchema.safeParse(data)

    if (!parsedData.success) {
      throw new Error('Invalid response schema: ' + parsedData.error.message)
    }

    return parsedData.data
  } catch (error) {
    // Sentry.captureException(error, {
    //   tags: { context: "createToken" },
    // });
    throw error
  }
}

/**
 * Hook to create LiveKit token using TanStack Query
 */
export const useCreateTokenQuery = (
  args: CreateTokenArgs,
  options?: Omit<UseQueryOptions<ConnectionDetailsResponse, Error>, 'queryKey' | 'queryFn'>,
) => {
  return useQuery({
    queryKey: ['livekit-token', args],
    queryFn: () => createLivekitToken(args),
    ...options,
  })
}
