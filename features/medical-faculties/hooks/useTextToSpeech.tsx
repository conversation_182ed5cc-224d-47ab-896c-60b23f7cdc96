import { MutationOptions, useMutation } from '@tanstack/react-query'
import { useRef } from 'react'
import { medicalFacultyService } from '../services/faculties.service'
import { TextToSpeechPayload } from '../types'
import { medicalFacultiesQueryKeys } from './queryKeys'

type TextToSpeechVariables = { payload: TextToSpeechPayload }

export const useTextToSpeech = ({
  options,
}: {
  options?: Omit<MutationOptions | undefined, 'mutationFn' | 'mutationKey'>
} = {}) => {
  const abortControllerRef = useRef<AbortController | null>(null)

  const {
    isError: isTextToSpeechError,
    isPending: isTextToSpeechPending,
    mutate: textToSpeechMutation,
    isSuccess: isTextToSpeechSuccess,
    ...rest
  } = useMutation({
    mutationKey: medicalFacultiesQueryKeys['text-to-speech'].base(),
    mutationFn: async ({ payload }: TextToSpeechVariables) => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      const abortController = new AbortController()
      abortControllerRef.current = abortController

      return medicalFacultyService.getTextToSpeech({
        payload,
        options: {
          signal: abortControllerRef.current.signal,
        },
      })
    },
    ...options,
  })

  return {
    isTextToSpeechError,
    isTextToSpeechPending,
    textToSpeechMutation,
    isTextToSpeechSuccess,
    ...rest,
  }
}
