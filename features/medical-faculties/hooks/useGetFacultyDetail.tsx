import { facultyService } from '@/features/faculty/services/faculty.service'
import { Faculty } from '@/types/faculty.type'
import { Params } from '@/types/http.type'
import { UseQueryOptions, useQuery } from '@tanstack/react-query'
import { AxiosRequestConfig } from 'axios'
import { medicalFacultiesQueryKeys } from './queryKeys'

export const useGetFacultyDetail = ({
  id,
  params = {},
  options = {},
  useQueryOptions,
}: {
  id: string
  params?: Params
  options?: AxiosRequestConfig
  useQueryOptions?: Omit<UseQueryOptions<Faculty | null>, 'queryKey' | 'queryFn'>
}) => {
  const {
    isError: isGetFacultyDetailError,
    isPending: isGetFacultyDetailLoading,
    data: facultyDetail,
    ...rest
  } = useQuery({
    queryKey: [medicalFacultiesQueryKeys['faculty-details'].base(), id, params],
    queryFn: async () =>
      facultyService.getFaculty({
        id,
        params: params,
        ...options,
      }),
    ...useQueryOptions,
  })
  return {
    isGetFacultyDetailError,
    isGetFacultyDetailLoading,
    facultyDetail,
    ...rest,
  }
}
