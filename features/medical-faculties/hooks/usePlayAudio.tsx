import { useAudioPlayer, useAudioPlayerStatus } from 'expo-audio'
import { useCallback, useEffect, useRef, useState } from 'react'

export const usePlayAudio = () => {
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)

  const audioPlayer = useAudioPlayer(currentAudioUrl ?? undefined)
  const audioPlayerStatus = useAudioPlayerStatus(audioPlayer)

  // Ref để track audio player hiện tại
  const currentPlayerRef = useRef<any>(null)

  // Update current player ref khi audioPlayer thay đổi
  useEffect(() => {
    if (audioPlayer) {
      currentPlayerRef.current = audioPlayer
    }
  }, [audioPlayer])

  // Handle audio loading states
  useEffect(() => {
    if (audioPlayerStatus) {
      setIsLoading(audioPlayerStatus.isLoaded === false && currentAudioUrl !== null)

      // Reset error when audio loads successfully
      if (audioPlayerStatus.isLoaded) {
        setError(null)
      }
    }
  }, [audioPlayerStatus, currentAudioUrl])

  const playAudioSafely = useCallback(
    async (player: any, url: string) => {
      if (!player || !url) {
        setError('Player hoặc URL không hợp lệ')
        return false
      }

      try {
        setError(null)
        setIsLoading(true)

        // Dừng audio hiện tại nếu đang phát
        if (audioPlayerStatus?.playing) {
          await player.pause()
        }

        // Seek về đầu và phát
        await player.seekTo(0)
        await player.play()

        setIsLoading(false)
        return true
      } catch (error) {
        console.error('Error playing audio:', error)
        setError('Không thể phát audio')
        setIsLoading(false)
        return false
      }
    },
    [audioPlayerStatus],
  )

  const handlePlayAudio = useCallback(
    async (audioUrl: string) => {
      if (!audioUrl) {
        setError('URL audio không hợp lệ')
        return false
      }

      try {
        // Nếu URL khác với URL hiện tại, cập nhật URL
        if (currentAudioUrl !== audioUrl) {
          setCurrentAudioUrl(audioUrl)
          // Đợi một chút để audioPlayer được tạo với URL mới
          setTimeout(() => {
            if (currentPlayerRef.current) {
              playAudioSafely(currentPlayerRef.current, audioUrl)
            }
          }, 100)
        } else {
          // Nếu cùng URL, phát lại từ đầu
          if (currentPlayerRef.current) {
            return await playAudioSafely(currentPlayerRef.current, audioUrl)
          }
        }
        return true
      } catch (error) {
        console.error('Error in handlePlayAudio:', error)
        setError('Lỗi khi phát audio')
        return false
      }
    },
    [currentAudioUrl, playAudioSafely],
  )

  const pauseAudio = useCallback(async () => {
    try {
      if (currentPlayerRef.current && audioPlayerStatus?.playing) {
        await currentPlayerRef.current.pause()
        return true
      }
      return false
    } catch (error) {
      console.error('Error pausing audio:', error)
      setError('Không thể tạm dừng audio')
      return false
    }
  }, [audioPlayerStatus])

  const stopAudio = useCallback(async () => {
    try {
      if (currentPlayerRef.current) {
        await currentPlayerRef.current.pause()
        await currentPlayerRef.current.seekTo(0)
        return true
      }
      return false
    } catch (error) {
      console.error('Error stopping audio:', error)
      setError('Không thể dừng audio')
      return false
    }
  }, [])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  return {
    // Main functions
    handlePlayAudio,
    pauseAudio,
    stopAudio,
    clearError,

    // States
    isLoading,
    error,
    isPlaying: audioPlayerStatus?.playing ?? false,
    currentAudioUrl,

    // Audio player status (for advanced usage)
    audioPlayerStatus,
    audioPlayer,
  }
}
