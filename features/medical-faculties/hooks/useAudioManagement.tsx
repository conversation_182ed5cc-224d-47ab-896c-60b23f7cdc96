import { LocaleEnum } from '@/enums/locale.enum'
import { useMedicalFacultiesStore } from '../stores/MedicalFacultiesStores'
import { TextToSpeechPayload } from '../types'
import { useTextToSpeech } from './useTextToSpeech'

export const useAudioManagement = () => {
  const { textToSpeechMutation, isTextToSpeechPending } = useTextToSpeech()

  const { saveAudio } = useMedicalFacultiesStore()

  const onGetAudio = (payload: TextToSpeechPayload, language: LocaleEnum) => {
    textToSpeechMutation(
      { payload },
      {
        onSuccess: (data) => {
          saveAudio(data, language)
        },
        onError: (error) => {
          console.error('Error fetching audio:', error)
        },
      },
    )
  }

  return {
    onGetAudio,
    isGetTextToSpeechPending: isTextToSpeechPending,
  }
}
