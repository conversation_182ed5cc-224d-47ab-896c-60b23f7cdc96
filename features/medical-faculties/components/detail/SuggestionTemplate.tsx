import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/global.type'
import { useTranslation } from 'react-i18next'
import { View } from 'react-native'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const SuggestionTemplate = () => {
  const { t } = useTranslation()
  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  const { getSampleSentences } = useMedicalFacultiesStore()

  const sampleSentences = getSampleSentences()

  if (sampleSentences.length === 0) return null
  return (
    <View className="mt-2 flex-col gap-3">
      <Text size="body6">{t('MES-1029')}</Text>

      {sampleSentences.map((item) => {
        return (
          <View key={item.id} className="gap-2 rounded-lg bg-[#F8F8FC] p-3">
            <Text size="body7" variant="subdued">
              {
                (item.sentence as unknown as LocalizeField<string>)[
                  primaryLanguage as keyof LocalizeField<string>
                ]
              }
            </Text>

            <Text size="body7" variant="subdued">
              {
                (item.sentence as unknown as LocalizeField<string>)[
                  secondaryLanguage as keyof LocalizeField<string>
                ]
              }
            </Text>
          </View>
        )
      })}
    </View>
  )
}
