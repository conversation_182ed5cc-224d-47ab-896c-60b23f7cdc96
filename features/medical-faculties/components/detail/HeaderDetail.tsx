import { Text } from '@/components/ui/Text/Text'
import { LocaleEnum } from '@/enums/locale.enum'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { useTranslation } from 'react-i18next'
import { TouchableOpacity, View } from 'react-native'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const HeaderDetail = () => {
  const { t: tVi } = useTranslation(undefined, {
    lng: 'vi',
  })

  const { t: tJa } = useTranslation(undefined, {
    lng: 'ja',
  })

  const { type, setType } = useMedicalFacultiesStore()

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()
  return (
    <View className="flex-row items-center justify-center">
      <TouchableOpacity onPress={() => setType('patient')} className="flex-1 shrink-0">
        <View
          className={`flex-1 items-center border-b ${type === 'patient' ? 'border-primary' : 'border-[#DDDDEE]'}  p-2 pb-3`}
        >
          <Text size="body8" variant={type === 'patient' ? 'primary' : 'subdued'}>
            {primaryLanguage === LocaleEnum.VI ? tVi('MES-1041') : tJa('MES-1041')}
          </Text>

          <Text size="body8" variant={type === 'patient' ? 'primary' : 'subdued'}>
            {secondaryLanguage === LocaleEnum.VI ? tVi('MES-1041') : tJa('MES-1041')}
          </Text>
        </View>
      </TouchableOpacity>

      <TouchableOpacity onPress={() => setType('doctor')} className="flex-1 shrink-0">
        <View
          className={`flex-1 items-center border-b ${type === 'doctor' ? 'border-primary' : 'border-[#DDDDEE]'}  p-2 pb-3`}
        >
          <Text size="body8" variant={type === 'doctor' ? 'primary' : 'subdued'}>
            {primaryLanguage === LocaleEnum.VI ? tVi('MES-1042') : tJa('MES-1042')}
          </Text>

          <Text size="body8" variant={type === 'doctor' ? 'primary' : 'subdued'}>
            {secondaryLanguage === LocaleEnum.VI ? tVi('MES-1042') : tJa('MES-1042')}
          </Text>
        </View>
      </TouchableOpacity>
    </View>
  )
}
