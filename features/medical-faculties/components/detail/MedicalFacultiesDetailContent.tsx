import { Text } from '@/components/ui/Text/Text'
import { useAuthentication } from '@/hooks/auth/useAuthentication'
// import { AudioSession } from '@livekit/react-native'
import { useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { Platform, TouchableOpacity, View } from 'react-native'
// import { useTranslateConnection } from '../../hooks/use-livekit-connection'
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { DescriptionBox } from './DescriptionBox'
import { HeaderDetail } from './HeaderDetail'
import { SuggestionTemplate } from './SuggestionTemplate'

export const MedicalFacultiesDetailContent = () => {
  const { t } = useTranslation()
  const { user } = useAuthentication()
  const [isAgentConnected, setIsAgentConnected] = useState(false)

  const { showLoading, type } = useMedicalFacultiesStore()

  // Start audio session
  // useEffect(() => {
  //   const start = async () => {
  //     await AudioSession.startAudioSession()
  //   }
  //   start()
  //   return () => {
  //     AudioSession.stopAudioSession()
  //     setIsAgentConnected(false)
  //   }
  // }, [])

  // const { connectionDetails, isLoading, error, refetch } = useTranslateConnection({
  //   agent_name: 'transcribe-agent-dev',
  //   participant_name: user?.name || 'User',
  //   participant_identity: user?.email || user?.id || 'user-identity',
  // })

  // Refetch connection details when screen comes into focus
  // useFocusEffect(
  //   useCallback(() => {
  //     refetch()
  //   }, [refetch]),
  // )

  const handleContinue = () => {
    showLoading()
  }
  const insets = useSafeAreaInsets()
  return (
    // <LiveKitRoom
    //   serverUrl={connectionDetails?.url}
    //   token={connectionDetails?.token}
    //   connect={isAgentConnected && !!connectionDetails?.token}
    //   audio={true}
    //   video={false}
    // >
    <View className="flex-1">
      <KeyboardAwareScrollView
        bottomOffset={insets.bottom + 16}
        showsHorizontalScrollIndicator={false}
        keyboardShouldPersistTaps="never"
        keyboardDismissMode="on-drag"
        className="h-full"
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        <HeaderDetail />

        {type === 'patient' ? (
          <Text size="body7" className="my-4">
            <Trans
              i18nKey="MES-998"
              components={{
                voice: (
                  <Text size="body10" variant="primary">
                    {t('MES-999')}
                  </Text>
                ),
              }}
            />{' '}
            :
          </Text>
        ) : (
          <Text size="body7" className="my-4" variant="error">
            <Trans
              i18nKey="MES-1044"
              components={{
                voice: (
                  <Text size="body10" variant="error">
                    {t('MES-999')}
                  </Text>
                ),
              }}
            />{' '}
            :
          </Text>
        )}

        <DescriptionBox
          isAgentConnected={isAgentConnected}
          onToggleConnection={() => setIsAgentConnected((prev) => !prev)}
        />

        <SuggestionTemplate />
      </KeyboardAwareScrollView>

      <View
        style={{
          paddingHorizontal: 16,
          boxShadow: '0px 4px 22px 0px #00000026',
          paddingBottom: Platform.OS === 'android' ? insets.bottom + 8 : 24,
        }}
      >
        <TouchableOpacity
          onPress={handleContinue}
          className="mt-4 w-full flex-row justify-center rounded-lg bg-primary-500 p-3"
        >
          <Text size="button3" variant="white">
            {t('MES-122')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
    // </LiveKitRoom>
  )
}
