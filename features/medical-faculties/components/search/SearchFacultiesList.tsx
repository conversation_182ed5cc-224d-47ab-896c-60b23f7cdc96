'use client'
import { LocaleEnum } from '@/enums/locale.enum'
import React, { useMemo } from 'react'
import { View } from 'react-native'
import { MedicalFacultyList } from '../MedicalFacultyList'
import { SearchTipsBox } from './SearchTipsBox'

export type FilterField = {
  path: string
  type: 'equals' | 'contains'
  isLocalized: boolean
}

const SEARCHABLE_FIELDS: FilterField[] = [
  { path: 'symptoms.name', type: 'equals', isLocalized: true },
  { path: 'name', type: 'equals', isLocalized: true },
  { path: 'bodyParts.name', type: 'equals', isLocalized: true },
  // Uncomment if keywords search is needed
  { path: 'questions.question.keywords.name', type: 'equals', isLocalized: true },
]

type SearchFacultiesListProps = {
  query: string[]
}
export const SearchFacultiesList: React.FC<SearchFacultiesListProps> = ({ query }) => {
  const generateQueryString = useMemo(() => {
    return SEARCHABLE_FIELDS?.flatMap((field) => {
      if (field.isLocalized) {
        // For localized fields, create filters for each locale
        return Object.values(LocaleEnum).flatMap((locale) => {
          const queryType = field.type || 'like'
          return query.map((term) => ({
            [`${field.path}.${locale}`]: {
              [queryType]: term,
            },
          }))
        })
      } else {
        // For non-localized fields, create direct filters
        const queryType = field.type || 'like'
        return query.map((term) => ({
          [field.path]: { [queryType]: term },
        }))
      }
    })
  }, [query])

  return (
    <View className="mt-4 h-full">
      {query.length > 0 ? (
        <MedicalFacultyList
          isShowTotal={true}
          isDisable={true}
          isOpenDefault={true}
          isHiddenHeader={true}
          filters={generateQueryString}
          isCall={query.length > 0}
        />
      ) : (
        <SearchTipsBox />
      )}
    </View>
  )
}
