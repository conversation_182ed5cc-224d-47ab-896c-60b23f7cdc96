import ArrowLeftIcon from '@/assets/icons/arrow-left.svg'
import { Text } from '@/components/ui/Text/Text'
import { useAppLanguage } from '@/hooks/common/useAppLanguage'
import { LocalizeField } from '@/types/global.type'
import { useRouter } from 'expo-router'
import { TouchableOpacity, View } from 'react-native'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'

export const HeaderBarDetail = () => {
  const router = useRouter()

  const { facultyDetail } = useMedicalFacultiesStore()

  const name = facultyDetail?.name as unknown as LocalizeField<string>

  const { primaryLanguage, secondaryLanguage } = useAppLanguage()

  return (
    <View className="flex flex-row items-center justify-between gap-2 px-4 py-3 ">
      <TouchableOpacity
        onPressIn={() => {
          if (router.canGoBack()) {
            router.back()
          } else {
            router.replace('/')
          }
        }}
      >
        <ArrowLeftIcon width={16} height={16} />
      </TouchableOpacity>

      <View className="items-center">
        <Text size="body3" variant="primary">
          {name?.[primaryLanguage as keyof LocalizeField<string>]}
        </Text>

        <Text size="body9" variant="subdued">
          ({name?.[secondaryLanguage as keyof LocalizeField<string>]})
        </Text>
      </View>

      <View className="h-[16px] w-[16px]"></View>
    </View>
  )
}
