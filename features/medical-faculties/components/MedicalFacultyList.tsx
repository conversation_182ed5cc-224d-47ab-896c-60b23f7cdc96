import { Text } from '@/components/ui/Text/Text'
import { Faculty } from '@/types/faculty.type'
import React, { useCallback, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import { ActivityIndicator, FlatList, View } from 'react-native'
import { useGetInfiniteMedicalFaculties } from '../hooks/useGetInfiniteMedicalFaculties'
import { MedicalFacultyItem } from './MedicalFacultyItem'

import EmptyBoxIcon from '@/assets/icons/empty-box.svg'
import { useSafeAreaInsets } from 'react-native-safe-area-context'

type MedicalFacultyListProps = {
  isCall?: boolean
  isHiddenHeader?: boolean
  filters?: {
    [field: string]: {
      [field: string]: string
    }
  }[]
  isOpenDefault?: boolean
  isDisable?: boolean
  isShowTotal?: boolean
  textTotal?: string
}
export const MedicalFacultyList: React.FC<MedicalFacultyListProps> = ({
  isCall = true,
  isHiddenHeader = false,
  filters,
  isOpenDefault = false,
  isDisable = false,
  isShowTotal = false,
  textTotal = 'MES-71',
}) => {
  const { t } = useTranslation()

  const {
    medicalFaculties,
    isGetMedicalFacultiesLoading,
    isGetMedicalFacultiesError,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useGetInfiniteMedicalFaculties({
    params: {
      limit: 15,
      locale: 'all',
      select: {
        questions: false,
        createdAt: false,
        updatedAt: false,
      },

      ...(!!filters && {
        where: {
          and: [
            {
              or: filters,
            },
          ],
        },
      }),
    },

    config: {
      enabled: isCall,
    },
  })

  const allFaculties = useMemo(() => {
    if (!medicalFaculties?.pages) return []
    return medicalFaculties.pages.flatMap((page) => page?.docs || [])
  }, [medicalFaculties])

  const totalFaculties = useMemo(() => {
    return medicalFaculties?.pages?.[0]?.totalDocs || 0
  }, [medicalFaculties])

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isGetMedicalFacultiesLoading) {
      fetchNextPage()
    }
  }, [hasNextPage, isGetMedicalFacultiesLoading, fetchNextPage])

  const renderItem = useCallback(
    ({ item }: { item: Faculty }) => {
      return (
        <MedicalFacultyItem isDisabled={isDisable} isOpenDefault={isOpenDefault} faculty={item} />
      )
    },
    [isDisable, isOpenDefault],
  )

  const renderFooter = useCallback(() => {
    if (!isFetchingNextPage) return null

    return (
      <View className="py-4">
        <ActivityIndicator size="small" />
      </View>
    )
  }, [isFetchingNextPage])

  const renderEmpty = useCallback(() => {
    if (isGetMedicalFacultiesLoading) {
      return (
        <View className="flex-1 items-center justify-center py-8">
          <ActivityIndicator size="large" />
        </View>
      )
    }

    if (isGetMedicalFacultiesError) {
      return (
        <View className="flex-1 items-center justify-center py-8">
          <Text size="body6" className="text-red-500">
            {t('MES-997')}
          </Text>
        </View>
      )
    }

    return (
      <View className="flex-1 items-center justify-center py-8">
        <EmptyBoxIcon />
        <Text size="body6" className="text-gray-500">
          {t('MES-996')}
        </Text>
      </View>
    )
  }, [isGetMedicalFacultiesLoading, isGetMedicalFacultiesError, t])

  const insets = useSafeAreaInsets()
  return (
    <View className="flex-1">
      {!isHiddenHeader && (
        <Text size="body3" className="my-4">
          {t('MES-130')}
        </Text>
      )}

      {isShowTotal && !isGetMedicalFacultiesLoading && (
        <Text size="body3" className="mb-4">
          {t(textTotal)} ({totalFaculties})
        </Text>
      )}

      <FlatList
        data={allFaculties}
        extraData={filters}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        contentContainerStyle={{
          gap: 8,
          paddingBottom: insets.bottom
        }}
      />
    </View>
  )
}
