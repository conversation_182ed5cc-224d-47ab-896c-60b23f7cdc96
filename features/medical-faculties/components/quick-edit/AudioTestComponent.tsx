import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { TouchableOpacity, View } from 'react-native'
import { usePlayAudio } from '../../hooks/usePlayAudio'

// Component để test hook usePlayAudio trong context của BoxSentences
export const AudioTestComponent = () => {
  const {
    handlePlayAudio,
    pauseAudio,
    stopAudio,
    clearError,
    isLoading,
    error,
    isPlaying,
    currentAudioUrl,
  } = usePlayAudio()

  // Sample audio URLs for testing
  const testAudioUrls = [
    'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    'https://file-examples.com/storage/fe68c1b7c1b58e2f89c4c95/2017/11/file_example_MP3_700KB.mp3',
    'https://www.learningcontainer.com/wp-content/uploads/2020/02/Kalimba.mp3',
  ]

  return (
    <View className="p-4 gap-4 bg-white rounded-lg shadow-sm">
      <Text size="body3" className="text-center font-semibold">
        🎵 Audio Player Test
      </Text>

      {/* Status Display */}
      <View className="bg-gray-50 p-3 rounded-lg">
        <Text size="body6" className="mb-1">
          <Text className="font-semibold">Trạng thái:</Text>{' '}
          {isLoading ? '⏳ Đang tải...' : isPlaying ? '▶️ Đang phát' : '⏸️ Dừng'}
        </Text>
        
        {currentAudioUrl && (
          <Text size="body8" className="text-gray-600">
            <Text className="font-semibold">Audio:</Text> {currentAudioUrl.split('/').pop()}
          </Text>
        )}
      </View>

      {/* Error Display */}
      {error && (
        <View className="bg-red-100 border border-red-300 rounded-lg p-3">
          <View className="flex-row items-center justify-between">
            <Text size="body7" className="text-red-600 flex-1">
              ❌ {error}
            </Text>
            <TouchableOpacity
              onPress={clearError}
              className="ml-2 bg-red-500 px-2 py-1 rounded"
            >
              <Text size="body8" className="text-white">✕</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Test Audio Buttons */}
      <View className="gap-2">
        <Text size="body6" className="font-semibold">Test Audio Files:</Text>
        {testAudioUrls.map((url, index) => (
          <TouchableOpacity
            key={index}
            onPress={() => handlePlayAudio(url)}
            disabled={isLoading}
            className={`p-3 rounded-lg border ${
              currentAudioUrl === url
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 bg-white'
            } ${isLoading ? 'opacity-50' : ''}`}
          >
            <Text size="body7" className="font-medium">
              🎵 Test Audio {index + 1}
              {currentAudioUrl === url && ' (Đang chọn)'}
            </Text>
            <Text size="body8" className="text-gray-500 mt-1">
              {url.split('/').pop()}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Control Buttons */}
      <View className="flex-row gap-2">
        <Button
          className="flex-1 bg-orange-500"
          onPress={pauseAudio}
          disabled={!isPlaying || isLoading}
        >
          <Text className="text-white">⏸️ Tạm dừng</Text>
        </Button>
        
        <Button
          className="flex-1 bg-red-500"
          onPress={stopAudio}
          disabled={!currentAudioUrl || isLoading}
        >
          <Text className="text-white">⏹️ Dừng</Text>
        </Button>
      </View>

      {/* Replay Button */}
      {currentAudioUrl && (
        <Button
          onPress={() => handlePlayAudio(currentAudioUrl)}
          disabled={isLoading}
          className="bg-green-500"
        >
          <Text className="text-white">
            🔄 Phát lại
          </Text>
        </Button>
      )}

      {/* Loading Indicator */}
      {isLoading && (
        <View className="bg-blue-100 p-3 rounded-lg">
          <Text size="body6" className="text-center text-blue-600">
            ⏳ Đang tải audio, vui lòng đợi...
          </Text>
        </View>
      )}

      {/* Instructions */}
      <View className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <Text size="body8" className="text-yellow-800">
          💡 <Text className="font-semibold">Hướng dẫn:</Text> Nhấn vào các audio test để thử nghiệm hook usePlayAudio. 
          Component này demo các tính năng chính của hook.
        </Text>
      </View>
    </View>
  )
}

// Simple audio button component for integration
export const SimpleAudioPlayButton = ({ 
  audioUrl, 
  label = 'Phát audio',
  size = 'md' 
}: { 
  audioUrl: string
  label?: string
  size?: 'sm' | 'md' | 'lg'
}) => {
  const { handlePlayAudio, isLoading, isPlaying, error } = usePlayAudio()

  const sizeClasses = {
    sm: 'px-2 py-1 text-xs',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  return (
    <View className="gap-1">
      <TouchableOpacity
        onPress={() => handlePlayAudio(audioUrl)}
        disabled={isLoading}
        className={`rounded-lg ${sizeClasses[size]} ${
          isLoading
            ? 'bg-gray-300'
            : isPlaying
            ? 'bg-green-500'
            : 'bg-blue-500'
        }`}
      >
        <Text className="text-white text-center font-medium">
          {isLoading ? '⏳' : isPlaying ? '▶️' : '🔊'} {label}
        </Text>
      </TouchableOpacity>
      
      {error && (
        <Text size="body8" className="text-red-500 text-center">
          {error}
        </Text>
      )}
    </View>
  )
}
