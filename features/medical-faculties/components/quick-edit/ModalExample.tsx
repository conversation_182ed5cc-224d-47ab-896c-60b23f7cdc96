import { Button } from '@/components/ui/Button/Button'
import { Text } from '@/components/ui/Text/Text'
import { useState } from 'react'
import { Modal, TouchableOpacity, View } from 'react-native'
import { useOpenConfirmDeleteModal } from './ConfirmDeleteModal'

// Ví dụ 1: Sử dụng Modal cơ bản của React Native
export const BasicModalExample = () => {
  const [isVisible, setIsVisible] = useState(false)

  return (
    <View className="p-4">
      <Button onPress={() => setIsVisible(true)}>
        <Text>Mở Modal Cơ Bản</Text>
      </Button>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setIsVisible(false)}
      >
        {/* Backdrop - overlay màu đen mờ */}
        <View className="flex-1 items-center justify-center bg-black/50 px-6">
          {/* Modal content - box chính ở giữa màn hình */}
          <View className="w-full max-w-sm rounded-xl bg-white p-6 shadow-lg">
            <Text size="body3" className="mb-4 text-center font-semibold">
              Modal Ở Giữa Màn Hình
            </Text>
            <Text size="body7" className="mb-6 text-center text-gray-600">
              Đây là một modal hiển thị ở giữa màn hình với backdrop mờ.
            </Text>
            
            <TouchableOpacity
              className="rounded-lg bg-blue-500 py-3"
              onPress={() => setIsVisible(false)}
            >
              <Text size="body6" className="text-center text-white">
                Đóng Modal
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  )
}

// Ví dụ 2: Sử dụng Dialog component có sẵn (Recommended)
export const DialogModalExample = () => {
  const { openConfirmDeleteModal } = useOpenConfirmDeleteModal()

  const handleOpenDialog = () => {
    openConfirmDeleteModal({
      title: 'Thông báo',
      description: 'Đây là modal sử dụng Dialog component với animation đẹp!',
      onConfirmDelete: () => {
        console.log('Đã xác nhận!')
      },
    })
  }

  return (
    <View className="p-4">
      <Button onPress={handleOpenDialog}>
        <Text>Mở Dialog Modal</Text>
      </Button>
    </View>
  )
}

// Ví dụ 3: Modal với nhiều tùy chọn
export const AdvancedModalExample = () => {
  const [isVisible, setIsVisible] = useState(false)
  const [selectedOption, setSelectedOption] = useState<string>('')

  const options = ['Tùy chọn 1', 'Tùy chọn 2', 'Tùy chọn 3']

  return (
    <View className="p-4">
      <Button onPress={() => setIsVisible(true)}>
        <Text>Mở Modal Nâng Cao</Text>
      </Button>

      <Modal
        visible={isVisible}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsVisible(false)}
      >
        <View className="flex-1 items-center justify-center bg-black/50 px-6">
          <View className="w-full max-w-md rounded-xl bg-white p-6">
            <Text size="body3" className="mb-4 text-center font-semibold">
              Chọn Tùy Chọn
            </Text>
            
            {options.map((option, index) => (
              <TouchableOpacity
                key={index}
                className={`mb-2 rounded-lg border p-3 ${
                  selectedOption === option ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                }`}
                onPress={() => setSelectedOption(option)}
              >
                <Text className="text-center">{option}</Text>
              </TouchableOpacity>
            ))}
            
            <View className="mt-4 flex-row gap-3">
              <TouchableOpacity
                className="flex-1 rounded-lg bg-gray-200 py-3"
                onPress={() => setIsVisible(false)}
              >
                <Text className="text-center">Hủy</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                className="flex-1 rounded-lg bg-blue-500 py-3"
                onPress={() => {
                  console.log('Đã chọn:', selectedOption)
                  setIsVisible(false)
                }}
              >
                <Text className="text-center text-white">Xác nhận</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </View>
  )
}
