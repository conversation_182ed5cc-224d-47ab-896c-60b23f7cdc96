import { Text } from "@/components/ui/Text/Text"
import { LocaleEnum } from "@/enums/locale.enum"
import { useAppLanguage } from "@/hooks/common/useAppLanguage"
import { LocalizeField } from "@/types/global.type"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { View } from "react-native"
import { useMedicalFacultiesStore } from "../../stores/MedicalFacultiesStores"

export const SymptomsInSentences = () => {
    const { t } = useTranslation()

    const { primaryLanguage, secondaryLanguage } = useAppLanguage()

    const { getKeywords } = useMedicalFacultiesStore()

    const keywords = useMemo(() => {
        return Object.values(getKeywords()) || []
    }, [getKeywords])

    return <View className="mt-4 gap-3 w-full">

        <Text size='body6'>
            {t('MES-1034')}
        </Text>
        {
            keywords?.map((keyword, index) => (
                <View key={index} className="px-4 py-3 rounded-lg bg-[#F9F9FC] flex-row gap-3 justify-between items-center">
                    <Text size="body7" >
                        {(keyword.name as unknown as LocalizeField<string>)[primaryLanguage as LocaleEnum]} </Text>
                    <Text size="body7" variant="subdued">
                        {(keyword.name as unknown as LocalizeField<string>)[secondaryLanguage as LocaleEnum]} </Text>
                </View>

            ))
        }
    </View>
}
