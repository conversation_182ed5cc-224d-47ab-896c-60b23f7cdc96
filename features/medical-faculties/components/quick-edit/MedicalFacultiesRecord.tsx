import { Text } from '@/components/ui/Text/Text'
import { useTranslation } from 'react-i18next'
import { ScrollView, TouchableOpacity, View } from 'react-native'
import { useSafeAreaInsets } from 'react-native-safe-area-context'
import { useMedicalFacultiesStore } from '../../stores/MedicalFacultiesStores'
import { HeaderDetail } from '../detail/HeaderDetail'
import { BoxSentences } from './BoxSentences'
import { SymptomsInSentences } from './SymptomsInSentences'

export const MedicalFacultiesRecord = () => {
  const { t } = useTranslation()

  const { setType } = useMedicalFacultiesStore()

  const insets = useSafeAreaInsets()

  const handleContinue = () => {
    setType('doctor')
  }

  return (
    <View className="flex-1">
      <ScrollView
        keyboardShouldPersistTaps="handled"
        keyboardDismissMode="on-drag"
        className="flex-1"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingHorizontal: 16 }}
      >
        <HeaderDetail />

        <Text className="mt-4" size="body9" variant="error">
          {t('MES-1033')}
        </Text>

        <BoxSentences />

        <SymptomsInSentences />
      </ScrollView>

      <View style={{ paddingBottom: insets.bottom + 16, paddingHorizontal: 16 }}>
        <TouchableOpacity
          onPress={handleContinue}
          className="mt-4 w-full flex-row justify-center rounded-lg bg-primary-500 p-3"
        >
          <Text size="button3" variant="white">
            {t('MES-1043')}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}
