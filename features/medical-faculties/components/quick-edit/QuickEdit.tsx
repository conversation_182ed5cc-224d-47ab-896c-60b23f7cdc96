import StarIcon from '@/assets/icons/star-fly.svg'
import { Text } from "@/components/ui/Text/Text"
import React from 'react'
import { useTranslation } from "react-i18next"
import { TouchableOpacity, View } from "react-native"


import { APP_ROUTES } from '@/routes/appRoutes'
import { Link } from 'expo-router'

export const QuickEditor = () => {
    const { t } = useTranslation()


    return <View>
        <Link asChild href={APP_ROUTES.MEDICAL_FACULTIES.children?.MEDICAL_FACULTIES_QUICK_EDIT.path}>
            <TouchableOpacity className="flex-row gap-1 items-center py-1 px-2 rounded-md bg-[##DBEAFE]">
                <StarIcon />
                <Text size="body8" variant="primary">
                    {t('MES-1035')}
                </Text>
            </TouchableOpacity>
        </Link>


    </View>

}

