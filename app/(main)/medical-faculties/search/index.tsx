import { MedicalFacultiesSearchWrapper } from '@/features/medical-faculties/screens/search/MedicalFacultiesSearchWrapper'
import { withAuthentication } from '@/hoc/withAuthentication'

import { SafeAreaView } from 'react-native-safe-area-context'

function MedicalFacultyAppScreen() {
  return (
    <SafeAreaView
      className="bg-white"
      style={{ flex: 1 }}
      edges={['left', 'top', 'right', 'bottom']}
    >
      <MedicalFacultiesSearchWrapper />
    </SafeAreaView>
  )
}

export default withAuthentication(MedicalFacultyAppScreen)
